# MMIS Sprint Plan v1.0 (Keystone Base)

> 目标：以 Keystone 为数据与后台中台，构建“我的信息系统（MMIS）”的 MVP 闭环：**捕获 → 编目 → 搜索 → 行动（提醒/跟进/续费） → 安全与导出**。

## 0. 项目概览

* **前台**：Next.js 15（App Router、TS、Tailwind、PWA、IndexedDB 缓存）。
* **中台**：Keystone（Prisma/Postgres、GraphQL、Admin UI、字段/列表访问控制、Hooks、extendGraphqlSchema）。
* **搜索**：Typesense/Meilisearch（关键词/分面；V2 接语义）。
* **对象存储**：Cloudflare R2（S3 兼容）。
* **定时/队列**：平台 Cron（Vercel/Workers）+ Keystone 自定义 Mutation。
* **安全**：最小化敏感数据存储（密码不入库，仅外部密码库条目 ID）；操作审计；导出。

**版本目标（12 周内即可）：** 先以 4–6 个 Sprint 交付 MVP 与 V1；下文给出每个 Sprint 的可交付物与验收标准。默认 Sprint=2 周，可按资源改为 1 周流。

---

## 团队与角色

* **PO**（你）：需求优先级、验收。
* **Tech Lead**：架构/DevOps/代码规范/性能。
* **BE**（Keystone/Prisma/GraphQL/Workers）。
* **FE**（Next.js/Tailwind/状态管理/组件）。
* **QA**（可兼职）：用例、回归、数据校验脚本。

> 统一管理：Issue/PR 模板、约定式提交、预览环境、E2E（Playwright）基线。

---

## DoR / DoD

**Definition of Ready（DoR）**

* 用户故事具备：目标/验收标准/字段与权限定义/原型草图。
* 外部依赖（域名、R2、DB、Typesense）开通完成。

**Definition of Done（DoD）**

* 代码已合并主干、通过 CI（lint、test）、有迁移脚本与回滚方案、Admin UI 可操作、API 文档更新、含最少 1 组 E2E 用例、更新变更日志。

---

## Sprint 0（基础设施与骨架｜2 周）

**目标**：项目初始化、流水线、环境分层、最小可运行的 Keystone + Next.js。

**用户故事**

* MMIS-0.1：作为开发者，我能本地一键启动前后端与数据库，并可跑首个健康检查。
* MMIS-0.2：作为管理员，我能通过 Admin UI 登录并看到空的列表导航（无数据）。

**主要任务**

* Monorepo/或双仓初始化：pnpm/Turborepo（可选）。
* Keystone 基础：会话/用户 List、RBAC 模型、环境变量管理、Prisma 迁移。
* Next.js 基础：布局、UI 组件库、全局状态（Zustand/Redux）与请求封装。
* CI/CD：ESLint/Prettier、tsc、Playwright 最小 E2E、Preview 部署。
* 基础监控与日志：请求日志、慢查询告警（开发环境）。

**交付物**

* 可访问的 Admin UI（/keystone）。
* 健康检查：`/api/health`。
* 文档：启动、部署、迁移与回滚说明。

**验收标准**

* 新同学 ≤30 分钟可跑起完整栈。
* 首次迁移可重复执行（幂等），回滚脚本可用。

---

## Sprint 1（核心数据模型 + Admin UI｜2 周）

**目标**：建立 MVP 的关键 List 与字段、访问控制与空间隔离；提供基本 CRUD。

**范围对象**：Space、Person、Org、Relation、Interaction、Asset、Tag、Account、Subscription、Reminder、AuditLog。

**用户故事**

* MMIS-1.1：我能在 Admin UI 维护人/组织/关系/互动记录，查看时间线。
* MMIS-1.2：我能录入资产（文件/网页/笔记）元数据与标签，并与人/组织关联。
* MMIS-1.3：我能创建账号目录与订阅到期项，但不存明文密码，仅引用外部密码库条目 ID。

**主要任务**

* 定义各 List 字段与关系（含 Space 外键）；
* 访问控制：按 session.spaceId 过滤；机密字段只读/隐藏；
* 文件/图片字段的 Storage：对接 R2（S3 endpoint、签名 URL）；
* Admin UI 配置（字段视图、默认排序、过滤、快捷创建）。

**交付物**

* GraphQL Schema（自动）+ 关键 List 的 Admin UI 界面；
* 首批种子数据（示例 Person/Asset/Subscription）。

**验收标准**

* 人/组织/资产/账号/订阅 CRUD 全可用；
* Space 隔离生效，跨 Space 数据不可见；
* 外部密码库仅存 `externalSecretRef`（不存明文）。

---

## Sprint 2（搜索与统一收件箱｜2 周）

**目标**：可用的全局搜索 + Inbox 待整理流；基础去重；前台最小可用界面。

**用户故事**

* MMIS-2.1：我能在前台用搜索框按关键词与筛选（类型/标签/人/组织/时间）找资料。
* MMIS-2.2：我能把网页/文件/摘录保存到 Inbox，并批量加标签/关联或“稍后处理”。
* MMIS-2.3：系统对相同 URL/checksum 的资产给出重复提示并支持合并。

**主要任务**

* 搜索索引服务落地（Typesense/Meilisearch）：索引结构与同步策略；
* Keystone Hooks：`afterOperation` 同步索引；重复检测（URL+checksum）；
* Next.js 前台：/inbox、/search、/assets/\[id] 页面；批量编辑与撤销；
* 基础高亮/摘要生成（可离线任务）。

**交付物**

* 可用的搜索列表页与 Inbox 页；
* 重复项标记与“合并”动作（保留最新元数据、合并关联）。

**验收标准**

* 搜索首屏 < 1s（索引命中）；
* Inbox 批量操作可撤销（10 秒内），写入审计。

---

## Sprint 3（个人 CRM 与未联系提醒｜2 周）

**目标**：人脉 360° 视图、互动记录、跟进策略与提醒中心。

**用户故事**

* MMIS-3.1：在某人卡片，我可记录互动并设置“X 天后提醒跟进”。
* MMIS-3.2：重要度≥阈值的联系人，超过设定间隔未互动会进入“需联系”列表与提醒中心。

**主要任务**

* 前台：/people、/people/\[id]、/orgs/\[id] 页面；时间线与相关资产聚合；
* Reminder 模型完善：类型（followup/expiry/…）、触发时间、状态流转；
* Cron Worker：每日计算“未联系”与到期触发；推送到提醒中心；
* 自定义 GraphQL：`touchInteraction`（写互动并更新计时）。

**交付物**

* 人/组织详情页（时间线、关联资产、下一步跟进）；
* 提醒中心（followup 类目）。

**验收标准**

* 设置 21 天后提醒能按时出现，并可完成/延后；
* 时间线写入与提醒生成有审计。

---

## Sprint 4（账号/订阅/域名到期与仪表盘｜2 周）

**目标**：到期提醒闭环、风险提示（未开 2FA）、首页仪表盘与每日摘要。

**用户故事**

* MMIS-4.1：我能为任一订阅/证照/域名设到期日+提前天数提醒，处理时一键生成下次提醒。
* MMIS-4.2：账号未开 2FA 显示风险徽章，并可记录“已开启”。
* MMIS-4.3：仪表盘显示今日待处理（到期/需联系/未整理）与最近新增。

**主要任务**

* 自定义 Mutation：`renewSubscription(id, newExpireAt)`；
* 账号风控：`mfa_enabled` 与风险标记；
* 仪表盘聚合卡片（Next.js）：今日待处理、最近捕获、风险提示；
* 每日摘要 Job：8:30 推送（站内通知/邮件可择一）。

**交付物**

* 到期提醒全流程、仪表盘与每日摘要。

**验收标准**

* 提醒可完成/延后/续期，续期生成下次触发；
* 仪表盘卡片可直达对应详情并可一键处理。

---

## Sprint 5（导入/导出与安全设置｜2 周）

**目标**：数据迁移能力、隐私与空间设置、操作审计完善。

**用户故事**

* MMIS-5.1：我能导入 CSV/VCF（联系人）、CSV（订阅/账号）；失败行有报告。
* MMIS-5.2：我能导出全部元数据与到期项 ICS；
* MMIS-5.3：我能在设置中关闭“正文抓取”，系统仅保留元数据与摘要（可安全清理历史正文）。

**主要任务**

* 导入向导：字段映射与预校验；
* 导出打包：JSON/CSV/ICS + 字段字典；
* 设置页：空间（工作/个人）与隐私开关；
* 审计：删除/导出/设置变更二次确认与留痕。

**交付物**

* 导入/导出工具、设置页、审计报表。

**验收标准**

* 导入可回滚（事务/中间表）；
* 导出包可在新环境重新导入；
* 隐私开关切换会对历史正文执行清理 Job 并记录审计。

---

## （可选）Sprint 6（浏览器扩展与重复检测增强｜2 周）

**目标**：极低摩擦捕获、去重/联结建议更智能。

**用户故事**

* MMIS-6.1：我能在浏览器点一次按钮保存标题/URL/选中文本到 Inbox。
* MMIS-6.2：系统基于标题/域名/历史关联，给出“可能关联的人/组织/项目”的建议。

**主要任务**

* 浏览器扩展：授权、域名白名单、错误上报；
* 联结建议服务：简单规则 + 关键词匹配（V2 可加向量）；
* 去重增强：相似标题+域名+时间窗口。

**交付物**

* 浏览器扩展（Chrome 起步）、联结建议 API。

**验收标准**

* 捕获到 Inbox ≤ 2 秒可见；
* 联结建议命中率在样本集≥70%。

---

## QA 策略与测试清单（跨 Sprint）

* 单元：List hooks、自定义 Mutation、日期与时区；
* 集成：索引同步、重复检测、续期逻辑；
* E2E：搜索 → 打开资产 → 生成任务/提醒 → 处理 → 审计可查；
* 回归：迁移脚本与回滚演练、导入/导出往返一致性；
* 安全：权限绕过测试、Space 隔离、敏感字段隐藏、审计完整性。

---

## 度量与里程碑

* 北极星：每周通过系统完成的“关键行动”次数（找回/跟进/续费/归档）。
* 性能：搜索首屏 <1s、仪表盘 <1s。
* 质量：到期漏报=0、未联系提醒转化≥50%、捕获→整理转化≥70%。

**里程碑**

* MVP 完成：Sprint 1–3 结束；
* V1 完成：Sprint 4–5 结束；
* 增强：Sprint 6。

---

## 风险与应对

* 录入摩擦高 → 先上 Inbox/批量编辑与撤销；
* 隐私顾虑 → 正文抓取默认关闭，支持一键清理；
* 索引一致性 → 增量重建脚本与双写校验；
* 时区问题 → 所有时间统一 UTC 存储、界面按时区渲染。

---

## 附：用户故事 ID 汇总（便于建 issue）

* MMIS-0.1/0.2
* MMIS-1.1/1.2/1.3
* MMIS-2.1/2.2/2.3
* MMIS-3.1/3.2
* MMIS-4.1/4.2/4.3
* MMIS-5.1/5.2/5.3
* MMIS-6.1/6.2

> 若按 1 周 Sprint，可将每个上面 Sprint 拆半；若人力紧张，先交付 Sprint 1–3 达成 MVP 闭环。

# 我的信息系统（MMIS）PRD v1.0 — Keystone Base

> 文档状态：Draft → Review → **Approved**（目标）
> 文档所有者（PO）：你
> 最近更新：2025-09-13
> 版本：v1.0（面向 MVP + V1 交付）

---

## 1. 背景 & 机会（Why Now）

* 个人资料、人脉、账号、订阅、文件与网页摘录长期分散在多工具（邮箱/云盘/密码库/日历/IM/笔记），**检索与跟进断层**。
* 现有通用工具（Notion/Obsidian/密码库/轻型 CRM）各具长处，但**缺少“人—账号—资料—到期/提醒”的统一闭环**。
* 目标打造一套**个人信息中台（MMIS）**：以**轻量编目 + 强检索 + 正交自动化**为核心，最小化敏感数据存储。

---

## 2. 产品愿景 & 定位

* 愿景：成为专业个人用户的“**第二大脑 + 私人中台**”，让**找人找资料找关键动作**变得迅速、可追溯且安全。
* 定位：不替代密码库/邮箱/网盘，**以引用与编排**为原则；提供**统一视图、统一搜索、统一提醒**。

---

## 3. 目标（Goals）与非目标（Non-Goals）

### 3.1 目标

1. **统一视图**：围绕“人/组织/项目”聚合账号、资料、互动与提醒。
2. **快速检索**：关键词 + 实体筛选（人/组织/标签/时间/类型）；V1 提供拼写容错/分面。
3. **自动提醒**：订阅/证照/域名到期、人脉未联系、重复项合并建议。
4. **最低摩擦捕获**：网页/文件/摘录一键入库，自动摘要与标签建议。
5. **安全可控**：不存明文密码；敏感正文抓取**默认关闭**；可导出、可回滚、可迁移。

### 3.2 非目标

* 自建密码库/IM 聚合/重型团队协作/银行账户直连。
* 取代专业文档编辑/在线协作（仅编目与跳转）。

---

## 4. 用户 & 场景（Persona & JTBD）

### 4.1 Persona

* **P1：独立开发者/工程师（核心）**：高并发的信息源、多个订阅与域名、广泛人脉。
* **P2：自由职业者/个体经营者（扩展）**：合同/票据/客户跟进需求明显。
* **P3：轻协作者（可选）**：助理/搭档，限流量、限可见范围。

### 4.2 关键场景（JTBD）

1.

> 当我需要追踪某个**人/组织**相关的一切时，能在一个界面看到互动历史、相关资料、账号与下一步行动。

2.

> 当我要**快速找资料**时，可以按关键词与实体过滤立刻定位，并支持打开原文件/链接或创建任务。

3.

> 当**到期**（订阅/域名/证照）或**未联系**时，系统自动提醒，我可一键续期/延后/记录处理。

4.

> 当我在浏览网页或读文档时，**一键捕获**到系统，自动生成摘要与标签建议，稍后再整理。

---

## 5. 竞争与可替代品（简述）

* **Notion/Obsidian**：强编辑/知识构建；弱到期/人脉/账号台账。
* **密码库（1P/Bitwarden）**：强凭证安全；弱人/资料/提醒编排。
* **CRM/GTDer**：强流程与销售漏斗；不适合个人跨域资料。
  **差异点**：MMIS 以\*\*“统一编目 + 统一搜索 + 统一提醒”\*\*的个人级中台能力为核心，**不存密码，仅做引用**。

---

## 6. 范围与版本（Scope & Phasing）

### 6.1 模块全景（12 域）

1. 统一收件箱/捕获（Inbox/Capture）
2. 知识与资料库（Assets/Knowledge）
3. 个人 CRM（People/Orgs/Relations/Interactions）
4. 账号目录（Accounts）
5. 订阅/证照/域名/SSL（Subscriptions/Licenses/Domains）
6. 项目/目标/任务（Projects/Goals/Tasks，轻量）
7. 日历/事件/日志（Calendar/Events/Journal，元数据）
8. 票据/财务归档（Receipts/Finance-lite）
9. 设备/资产盘点（Devices/Warranty）
10. 学习/研究记录（Learning/Research）
11. 行程与证件（Travel/ID，可选）
12. 作品集与发布（Portfolio，可选）

### 6.2 MVP（约 6–8 周）

* Inbox/捕获、资料库 + 搜索、个人 CRM（人/组织/互动）、账号/订阅到期、提醒中心、设置与导出、仪表盘。
* 安全底座：空间隔离（工作/个人）、审计、最小化存储。

### 6.3 V1（再 4–6 周）

* 浏览器扩展、去重合并、OCR 与文本抽取、CSV/VCF 导入、拼写容错搜索、每日摘要邮件。
* 风险提示（未开 2FA/共享账号）。

### 6.4 V2（可选）

* 语义搜索/相似推荐、关系图谱、联结建议、规则引擎（IFTTT 风格）、作品集页。

---

## 7. 详细功能需求（FRD）

### 7.1 统一收件箱 & 捕获

**描述**：网页/文件/摘录统一进入 Inbox，支持“稍后整理”。
**关键能力**：一键保存（快捷键/分享面板/上传）、自动摘要、标签/关联建议、批量编辑、撤销。

**用户故事 & 验收**

* US-A1：快捷保存网页（标题/URL/选中文本）→ Inbox ≤ 2s 回显。
* US-A2：Inbox 批量加标签/关联/“稍后处理”；10s 内可撤销；操作入审计。
* US-A3：重复检测（同 URL 或同 checksum）在列表标记并提供合并。

**边界/异常**：离线保存进入本地队列；重复合并保留最新元数据与所有关联。

---

### 7.2 知识与资料库（Assets）

**描述**：文件/网页/笔记统一编目，支持标签、实体关联与版本。
**能力点**：元数据字段（标题/来源/摘要/标签/关联/时间）、全文索引（V1）、版本记录、外链跳转。

**用户故事 & 验收**

* US-B1：用 `type:pdf tag:合同 person:张三 2024..2025` 定位目标，首屏 < 1s（索引命中）。
* US-B2：资产详情可一键打开原文件/URL、转任务、分享临时链接（可设有效期）。

**边界**：默认不抓正文；开启后可按资产级别关闭，切换需清理历史正文并记录审计。

---

### 7.3 个人 CRM（People/Orgs/Relations/Interactions）

**描述**：人/组织 360°，时间线聚合互动与相关资产；未联系提醒。
**能力点**：重要度、最近互动、下一步跟进、关系强度、互动类型与来源。

**用户故事 & 验收**

* US-C1：记录一次互动并设“21 天后提醒”，到期前进入提醒中心与每日摘要。
* US-C2：重要度≥阈值且 X 天未互动 → 自动进入“需联系”列表，可完成/延后。
* US-C3：从人脉详情可直接联动：创建任务/关联资产/发起续期动作（如相关订阅）。

---

### 7.4 账号目录 & 订阅/证照/域名

**描述**：账号条目（仅元数据 + 外部密码库条目 ID）、订阅/域名/证照到期提醒与续期流水。
**能力点**：2FA 状态、到期日、成本周期、自动续费标记、续期历史。

**用户故事 & 验收**

* US-D1：为条目设“到期日 + 提前 30/7/1 天提醒”，处理时“续至 YYYY-MM-DD”并生成下次提醒。
* US-D2：未开启 2FA 的账号显示风险徽章；点击可记录“已开启”。

**边界**：不存明文密码/密钥；仅存外部密码库条目 ID。

---

### 7.5 搜索与导航

**描述**：全局搜索 + 实体/时间/标签筛选；结果按类型聚合；快速动作。
**验收**：首屏 < 1s；支持保存搜索（V1）；拼写容错/分面（V1）。

---

### 7.6 提醒中心 & 仪表盘 & 摘要

**描述**：统一提醒收敛（到期/未联系/重复合并/系统提示）、仪表盘“今日待处理/最近新增/风险提示”、每日摘要（V1）。
**验收**：提醒可完成/延后/忽略并留痕；仪表盘卡片可直达详情。

---

### 7.7 导入/导出 & 设置

**描述**：CSV/VCF 导入、JSON/CSV/ICS 导出；隐私与空间（工作/个人）隔离；正文抓取开关。
**验收**：导入前字段映射与校验、失败行报告；导出含字段字典；设置变更有二次确认与审计。

---

## 8. 用户旅程与关键流程（UJM/Flows）

1. **捕获 → 整理**：网页/文件 → Inbox 回显 → 批量加标签/关联 → 入库。
2. **搜索 → 行动**：关键词/筛选 → 结果聚合 → 打开/转任务/分享/续期。
3. **人脉跟进**：查看人卡片 → 记录互动 → 设置下一步 → 到期提醒。
4. **到期闭环**：提醒中心 → 续期/延后/完成 → 生成下次提醒 → 审计。

---

## 9. 信息架构（IA）与对象模型（产品视角）

**核心对象**：Space、Person、Org、Relation、Interaction、Account、Subscription、Asset、Tag、Task、Reminder、AuditLog。
**关键字段（精要）**：

* Person：name、emails\[]、phones\[]、org、importance、last\_interaction\_at、next\_follow\_up\_at、tags\[]。
* Org：name、domain、tags\[]、notes。
* Relation：a\_type/a\_id、b\_type/b\_id、type、strength、since。
* Interaction：subject、type、datetime、participants\[]、note\_summary、source\_ref。
* Account：service\_name、username、externalSecretRef、mfa\_enabled、expire\_at、notes。
* Subscription：item\_name、provider、expire\_at、cost\_period、auto\_renew、history\[]。
* Asset：type(file/web/note)、title、source\_url\_or\_path、captured\_at、summary、tags\[]、linked\_entities\[]、checksum。
* Tag：path、color、icon。
* Task：title、due\_at、assignees\[]、related\_entities\[]、status。
* Reminder：type(expiry/followup/…)、trigger\_at、target\_ref、status。
* AuditLog：actor、action、entity\_type、entity\_id、ts、diff。

**空间隔离**：所有对象含 `space` 外键；按空间与权限过滤可见性。

---

## 10. 权限与安全（Security & Privacy）

* **最小化存储**：不存明文密码/密钥/恢复码；正文抓取默认关闭。
* **密级**：公开/私有/机密（账号与到期为机密）；机密字段可隐藏/只读。
* **访问控制**：基于 Space 与会话的过滤；敏感操作二次确认。
* **审计**：导出/删除/设置变更/续期/合并等关键动作全部留痕。
* **备份与恢复**：每日自动备份（可选），演练恢复流程；3-2-1 策略。
* **数据可携带**：JSON/CSV/ICS 导出；字段字典与导入回放脚本。

---

## 11. 集成策略（Integrations）

* **密码库**：仅存条目 ID（1P/Bitwarden 等），前台跳转打开。
* **对象存储**：Cloudflare R2（S3 兼容）用于文件与图片；数据库仅存元数据与签名 URL。
* **搜索**：Typesense/Meilisearch；Keystone Hooks（afterOperation）同步索引。
* **定时/队列**：Cron（Vercel/Workers）触发 Keystone 自定义 Mutation（未联系/到期等）。
* **日历/邮件**（可选）：仅摄取元数据与标题，摘要化存储。

---

## 12. 非功能性需求（NFR）

* **可用性**：核心功能月可用性 ≥ 99.5%。
* **性能**：首屏与搜索 < 1s（索引命中）；列表分页 < 300ms。
* **可靠性**：到期提醒漏报率 = 0；导入/导出往返一致。
* **可维护性**：迁移脚本可回滚；配置与密钥分环境管理。
* **离线**：捕获与查看最近项可离线；在线后自动同步。
* **可访问性**：键盘可达、对比度与语义化标签满足基础标准。
* **国际化**：首批中/英；日期/数值/时区正确渲染。

---

## 13. 指标与遥测（Metrics & Telemetry）

* **北极星**：每周通过系统完成的“关键行动”次数（找回/跟进/续费/归档）。
* 行为指标：搜索命中率（首屏≥80%）、未联系提醒转化（≥50%）、捕获→整理转化（≥70%）。
* 系统指标：提醒送达率、索引滞后、失败率、平均响应时间。
* 隐私：仅采集必要匿名遥测；关闭开关。

---

## 14. 里程碑与发布（Milestones & Releases）

* **MVP**：完成模块 7.1\~7.6 的 Must；搜索命中与提醒闭环达标；导出与审计可用。
* **V1**：扩展、OCR、导入、拼写容错、每日摘要、2FA 风险提示。
* **V2**：语义/图谱/规则引擎/作品集。
* 详见《MMIS Sprint Plan v1.0（Keystone Base）》。

---

## 15. 风险 & 对策

* **录入摩擦高** → Inbox、批量编辑与撤销、自动标签/联结建议。
* **隐私顾虑** → 默认仅元数据；正文抓取为可选并可一键清理历史。
* **索引一致性** → 双写校验、增量重建脚本与重放队列。
* **时区/时间线混乱** → 统一 UTC 存储，界面按本地时区渲染。
* **范围膨胀** → MoSCoW 管控，MVP 硬边界：不做密码存储/IM 聚合。

---

## 16. 依赖与约束（Tech Constraints）

* **平台**：Keystone（Prisma/Postgres、GraphQL、Admin UI、细粒度访问控制）。
* **搜索**：Typesense/Meilisearch（外部服务）；
* **存储**：R2（S3 兼容）；
* **前端**：Next.js 15 + Tailwind + PWA；
* **任务**：Cron/Workers + Keystone 自定义 Mutation。
* **安全**：最小化敏感数据存储、空间隔离、操作审计。

---

## 17. 开放问题（Open Questions）

1. 每日摘要通知渠道：站内 vs 邮件（默认站内，邮件为可选）？
2. 正文抓取默认关闭；是否为某些白名单源（如个人私有文档）允许自动抓取？
3. 导出格式的优先级：JSON/CSV/ICS 是否需要一键生成“迁移脚本”？
4. 轻任务是否需要与外部 Todo 工具（Things/TickTick）互通？

---

## 18. 附录 A：验收一览（MVP）

* 搜索首屏 <1s；能按类型/标签/人/组织/时间筛选并叠加。
* Inbox 批量编辑可撤销（10s），写入审计。
* 人脉 360°：3 步内完成“记录互动 → 设跟进 → 关联资产”。
* 到期提醒：配置提前天数；处理后自动生成下次提醒；续期历史可查。
* 账号条目：显示 2FA 状态与风险徽章；外部密码库跳转。
* 导入：字段映射、失败行报告与回滚；导出含字段字典。
* 设置：正文抓取默认关闭，切换触发清理并记审计；空间隔离生效。

---

## 19. 附录 B：CSV 导入模板（字段摘要）

**People.csv**：name, email, phone, org, importance(1-5), tags
**Accounts.csv**：service\_name, username, externalSecretRef, mfa\_enabled(true/false), expire\_at, notes
**Subscriptions.csv**：item\_name, provider, expire\_at(YYYY-MM-DD), cost\_period(monthly/annual), auto\_renew(true/false)
**Assets.csv**：type(file/web/note), title, source\_url\_or\_path, captured\_at, tags, linked\_entities

---

## 20. 附录 C：API 形态（示例，产品级）

> 实际以 Keystone 自动生成的 GraphQL 为准，这里给出典型操作意图。

* **Query**：searchAssets(query, filters) → AssetConnection（含高亮/分面）
* **Mutation**：captureAsset(input) → Asset
* **Mutation**：mergeAssets(primaryId, duplicateId) → Asset
* **Mutation**：touchInteraction(personId, note, datetime?) → Interaction
* **Mutation**：createReminder(input) / completeReminder(id) / postponeReminder(id, days)
* **Mutation**：renewSubscription(id, newExpireAt) → Subscription

---

## 21. 术语表（Glossary）

* **资产（Asset）**：文件/网页/笔记等资料的编目对象（默认仅元数据）。
* **外部密码库条目 ID（externalSecretRef）**：指向 1P/Bitwarden 中的具体条目。
* **空间（Space）**：工作/个人等数据隔离单元。
* **提醒（Reminder）**：到期/未联系/重复合并/系统提示等待办。

---

> 本 PRD 与《MMIS Sprint Plan v1.0（Keystone Base）》配套使用：PRD 负责**做什么**与**为什么**；Sprint 计划负责**何时**与**怎么做**。
